import { useState, useCallback } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import type { Database } from '@/types/database'

type Response = Database['public']['Tables']['responses']['Insert']

export function useQuizResponses() {
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuth()

  const saveResponses = useCallback(async (answers: Record<string, string>) => {
    if (!user) {
      throw new Error('User must be authenticated to save responses')
    }

    try {
      setSaving(true)
      setError(null)

      // Convert answers to response format
      const responses: Response[] = Object.entries(answers).map(([questionId, answer]) => ({
        user_id: user.id,
        question_id: questionId,
        answer: answer
      }))

      // First, delete any existing responses for this user
      await supabase
        .from('responses')
        .delete()
        .eq('user_id', user.id)

      // Then insert new responses
      const { error: insertError } = await supabase
        .from('responses')
        .insert(responses)

      if (insertError) {
        throw insertError
      }

      return responses
    } catch (err) {
      console.error('Error saving responses:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to save responses'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setSaving(false)
    }
  }, [user])

  return { saveResponses, saving, error }
}