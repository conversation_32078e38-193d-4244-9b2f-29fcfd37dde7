import { useState, useEffect } from 'react'
import { Landing } from '@/pages/Landing'
import { PreTest } from '@/pages/PreTest'
import { Quiz } from '@/pages/Quiz'
import { Payment } from '@/pages/Payment'
import { Results } from '@/pages/Results'
import { useReportGeneration } from '@/hooks/useReportGeneration'
import { AuthProvider } from '@/contexts/AuthContext'
import { AuthGuard } from '@/components/auth/AuthGuard'

type AppState = 'landing' | 'pretest' | 'quiz' | 'results' | 'payment' | 'complete'

function App() {
  const [currentState, setCurrentState] = useState<AppState>('landing')
  const [quizAnswers, setQuizAnswers] = useState<Record<string, any>>({})
  const [isDirectPayment, setIsDirectPayment] = useState(false)
  const { generateReport, generating } = useReportGeneration()

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const directParam = urlParams.get('direct')
    if (directParam === 'payment') {
      setIsDirectPayment(true)
      setCurrentState('quiz')
    }
  }, [])

  const handleStartQuiz = () => {
    setCurrentState('pretest')
  }

  const handleStartActualQuiz = () => {
    setCurrentState('quiz')
  }

  const handleQuizComplete = (answers: Record<string, any>) => {
    setQuizAnswers(answers)
    setCurrentState('payment')
  }

  const handlePaymentComplete = () => {
    setCurrentState('results')
  }

  const handleBackToQuiz = () => {
    setCurrentState('quiz')
  }


  const renderCurrentState = () => {
    switch (currentState) {
      case 'landing':
        return <Landing onStartQuiz={handleStartQuiz} />
      case 'pretest':
        return <PreTest onStartQuiz={handleStartActualQuiz} />
      case 'quiz':
        return <Quiz onComplete={handleQuizComplete} isDirectPayment={isDirectPayment} />
      case 'payment':
        return <Payment onPaymentComplete={handlePaymentComplete} onBack={handleBackToQuiz} />
      case 'results':
        return <Results answers={quizAnswers} />
      case 'complete':
        return (
          <div className="min-h-screen flex items-center justify-center p-4">
            <div className="text-center space-y-4 max-w-2xl">
              <div className="text-6xl">✅</div>
              <h2 className="text-3xl font-bold">Payment Successful!</h2>
              <p className="text-muted-foreground text-lg">
                Your personalized supplement report has been generated and downloaded automatically.
              </p>
              <p className="text-sm text-muted-foreground">
                Check your Downloads folder for your PDF report. If the download didn't start automatically, 
                please check your browser's download settings.
              </p>
              <div className="pt-4">
                <button 
                  className="bg-secondary text-secondary-foreground px-6 py-3 rounded-lg font-semibold mr-4"
                  onClick={() => setCurrentState('landing')}
                >
                  Take Another Quiz
                </button>
                <button 
                  className="bg-primary text-primary-foreground px-6 py-3 rounded-lg font-semibold"
                  onClick={async () => {
                    try {
                      await generateReport(quizAnswers)
                    } catch (error) {
                      console.error('Error generating PDF:', error)
                      alert('There was an error generating your report. Please try again.')
                    }
                  }}
                  disabled={generating}
                >
                  {generating ? 'Generating...' : 'Download Report Again'}
                </button>
              </div>
            </div>
          </div>
        )
      default:
        return <Landing onStartQuiz={handleStartQuiz} />
    }
  }

  return (
    <AuthProvider>
      <AuthGuard>
        {renderCurrentState()}
      </AuthGuard>
    </AuthProvider>
  )
}

export default App