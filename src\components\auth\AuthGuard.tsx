import { useAuth } from '@/contexts/AuthContext'
import { AuthForm } from './AuthForm'
import { Button } from '@/components/ui/button'
import { useState } from 'react'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
}

export function AuthGuard({ children, requireAuth = false }: AuthGuardProps) {
  const { user, loading, signOut, isConfigured } = useAuth()
  const [authMode, setAuthMode] = useState<'login' | 'signup'>('login')
  const [showAuth, setShowAuth] = useState(false)

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  // If auth is not configured, show children directly
  if (!isConfigured) {
    return <>{children}</>
  }

  // If user is authenticated, show children with user info
  if (user) {
    return (
      <div className="min-h-screen">
        {/* User Navigation Bar */}
        <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <h1 className="text-xl font-bold">QuizHub</h1>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-muted-foreground">
                  Welcome, {user.email}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={signOut}
                >
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        </nav>
        {children}
      </div>
    )
  }

  // If auth is required and user is not authenticated, show auth form
  if (requireAuth) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <AuthForm
          mode={authMode}
          onToggleMode={() => setAuthMode(authMode === 'login' ? 'signup' : 'login')}
          onSuccess={() => {
            // This will be called when auth succeeds or when user continues without account
            if (!user) {
              // User chose to continue without account
              setShowAuth(false)
            }
          }}
        />
      </div>
    )
  }

  // If auth is optional, show children with option to authenticate
  return (
    <div className="min-h-screen">
      {!showAuth && (
        <>
          {/* Optional Auth Bar */}
          <div className="bg-secondary/50 border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center h-12">
                <p className="text-sm text-muted-foreground">
                  Sign in to save your results and access your health history
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAuth(true)}
                >
                  Sign In / Sign Up
                </Button>
              </div>
            </div>
          </div>
          {children}
        </>
      )}
      
      {showAuth && (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
          <AuthForm
            mode={authMode}
            onToggleMode={() => setAuthMode(authMode === 'login' ? 'signup' : 'login')}
            onSuccess={() => setShowAuth(false)}
          />
        </div>
      )}
    </div>
  )
}