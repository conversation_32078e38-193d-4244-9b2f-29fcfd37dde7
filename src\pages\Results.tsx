import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useQuizResultsAPI, type QuizResult, type HealthTag } from '@/hooks/useQuizResultsAPI'
import { useQuizResponses } from '@/hooks/useQuizResponses'
import { useAuth } from '@/contexts/AuthContext'
import { Loader2, AlertTriangle, Pill, Utensils, ShoppingCart, Target, Shield, Download, TrendingUp } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { generateComprehensivePDF } from '@/utils/htmlToPdf'

interface ResultsProps {
  answers: Record<string, any>
}


interface Recommendation {
  type: 'supplement' | 'food'
  name: string
  description: string
  benefits: string[]
  // Enhanced supplement fields
  side_effects?: string[]
  contraindications?: string[]
  dosage_info?: {
    min_dose?: string
    max_dose?: string
    timing?: string
    form?: string
    with_food?: boolean
  }
  interactions?: string[]
  pregnancy_safe?: boolean
  breastfeeding_safe?: boolean
  rationale?: string
  // Food fields
  serving_suggestions?: string[]
  nutritional_info?: any
  priority?: number
  score?: number
  // Multi-condition fields
  condition_count?: number
  all_conditions?: string[]
}

// Convert database results to component format
function convertResults(results: QuizResult[]): {
  recommendations: Recommendation[]
} {
  const recommendations: Recommendation[] = []

  results.forEach(result => {
    // Add recommendation
    recommendations.push({
      type: result.recommendation_type,
      name: result.recommendation_name,
      description: result.recommendation_details.description,
      benefits: result.recommendation_details.benefits,
      side_effects: result.recommendation_details.side_effects,
      contraindications: result.recommendation_details.contraindications,
      dosage_info: result.recommendation_details.dosage_info,
      interactions: result.recommendation_details.interactions,
      pregnancy_safe: result.recommendation_details.pregnancy_safe,
      breastfeeding_safe: result.recommendation_details.breastfeeding_safe,
      rationale: result.recommendation_details.rationale,
      serving_suggestions: result.recommendation_details.serving_suggestions,
      nutritional_info: result.recommendation_details.nutritional_info,
      priority: result.priority,
      score: result.score,
      condition_count: result.condition_count || result.recommendation_details.condition_count,
      all_conditions: result.all_conditions || result.recommendation_details.all_conditions
    })
  })

  return {
    recommendations
  }
}

export function Results({ answers }: ResultsProps) {
  const { user } = useAuth()
  const { processResults, processing, error: processError } = useQuizResultsAPI()
  const { saveResponses, saving, error: saveError } = useQuizResponses()
  const [results, setResults] = useState<QuizResult[]>([])
  const [identifiedHealthTags, setIdentifiedHealthTags] = useState<HealthTag[]>([])
  const [hasProcessed, setHasProcessed] = useState(false)
  const [generatingPDF, setGeneratingPDF] = useState(false)

  const handleDownloadPDF = async () => {
    try {
      setGeneratingPDF(true)
      await generateComprehensivePDF('comprehensive-health-report')
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('There was an error generating the PDF. Please try again.')
    } finally {
      setGeneratingPDF(false)
    }
  }

  useEffect(() => {
    if (hasProcessed) return

    const handleResults = async () => {
      try {
        console.log('Starting results processing with answers:', answers)
        
        // Save responses to database if user is authenticated (don't await to avoid hanging)
        if (user) {
          saveResponses(answers).catch(err => 
            console.error('Error saving responses:', err)
          )
        }
        
        // Process quiz results
        console.log('Processing quiz results...')
        const { results: quizResults, identifiedHealthTags: healthTags } = await processResults(answers)
        console.log('Quiz results processed:', quizResults)
        console.log('Identified health tags:', healthTags)
        setResults(quizResults)
        setIdentifiedHealthTags(healthTags)
        setHasProcessed(true)
      } catch (err) {
        console.error('Error handling quiz results:', err)
        // Set empty results on error so component doesn't hang
        setResults([])
        setIdentifiedHealthTags([])
        setHasProcessed(true)
      }
    }

    handleResults()
  }, [answers, user, processResults, saveResponses, hasProcessed])

  // Show loading state
  if (!hasProcessed || processing || saving) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="flex items-center justify-center p-8">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Processing your results...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show error state
  if (processError || saveError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                Failed to process results: {processError || saveError}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Use the identified health tags from API instead of extracting from limited results  
  const healthTags = identifiedHealthTags
  let { recommendations } = convertResults(results)

  // Add fallback recommendations if none found
  if (results.length === 0 && hasProcessed && !processing) {
    recommendations = [
      {
        type: 'supplement',
        name: 'Daily Multivitamin',
        description: 'A comprehensive nutritional foundation',
        benefits: ['Fills nutritional gaps', 'Supports overall health', 'Immune function', 'Energy metabolism'],
        dosage_info: {
          min_dose: '1 tablet daily',
          timing: 'Morning with food',
          with_food: true
        },
        rationale: 'General wellness support when specific health areas are not identified'
      },
      {
        type: 'food',
        name: 'Leafy Green Vegetables',
        description: 'Nutrient-dense foundation foods',
        benefits: ['High in folate', 'Rich in iron', 'Antioxidant properties', 'Fiber for gut health'],
        serving_suggestions: ['Aim for 2-3 cups daily', 'Mix into smoothies', 'Sauté with garlic and olive oil']
      }
    ]
  }

  // Sort supplements by priority score (higher score = higher priority)
  const supplements = recommendations
    .filter(r => r.type === 'supplement')
    .sort((a, b) => (b.score || 0) - (a.score || 0))
  const foods = recommendations.filter(r => r.type === 'food')

  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const SafetyIndicator = ({ supplement }: { supplement: Recommendation }) => {
    const hasWarnings = supplement.pregnancy_safe === false || supplement.breastfeeding_safe === false
    if (!hasWarnings) return null

    return (
      <Alert variant="destructive" className="mt-2">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-sm">
          {supplement.pregnancy_safe === false && "Not safe during pregnancy"}
          {supplement.pregnancy_safe === false && supplement.breastfeeding_safe === false && " • "}
          {supplement.breastfeeding_safe === false && "Not safe while breastfeeding"}
        </AlertDescription>
      </Alert>
    )
  }

  const PriorityBadge = ({ score }: { score?: number }) => {
    if (!score) return null
    
    const level = score > 7 ? 'HIGH' : score > 4 ? 'MEDIUM' : 'LOW'
    const className = score > 7 ? 'bg-red-100 text-red-800' : 
                     score > 4 ? 'bg-yellow-100 text-yellow-800' : 
                     'bg-green-100 text-green-800'
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${className}`}>
        {level} PRIORITY
      </span>
    )
  }

  const generateSummary = () => {
    if (healthTags.length > 0) {
      const multiConditionSupplements = supplements.filter(s => s.condition_count && s.condition_count > 1)
      let summary = `Based on your responses, we identified ${healthTags.length} key health area${healthTags.length > 1 ? 's' : ''} to focus on. We recommend ${supplements.length} supplement${supplements.length > 1 ? 's' : ''} and ${foods.length} food recommendation${foods.length > 1 ? 's' : ''} to support your health goals.`
      
      if (multiConditionSupplements.length > 0) {
        summary += ` ${multiConditionSupplements.length} of our supplement recommendations address multiple health areas simultaneously, providing comprehensive support.`
      }
      
      return summary
    }
    return "Based on your responses, you have a solid health foundation. Our recommendations will help you maintain and optimize your wellness."
  }

  return (
    <div id="comprehensive-health-report" className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white print:bg-blue-600">
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="flex items-center justify-between mb-4">
            <div></div>
            <Button
              onClick={handleDownloadPDF}
              disabled={generatingPDF}
              variant="outline"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 print:hidden"
            >
              <Download className="h-4 w-4 mr-2" />
              {generatingPDF ? 'Generating PDF...' : 'Download PDF Report'}
            </Button>
          </div>
          <h1 className="text-4xl font-bold mb-2">Your Personalized Health Report</h1>
          <p className="text-blue-100 text-lg">Generated by QuizHub • {currentDate}</p>
          
          {/* Health Tag Badges under title - only matching quiz results */}
          {healthTags.length > 0 && (
            <div className="mt-4 mb-2">
              <p className="text-blue-100 text-sm mb-2">Potential issues identified:</p>
              <div className="flex flex-wrap gap-2">
                {healthTags.map((tag, index) => (
                  <span 
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white border border-white/30"
                  >
                    {tag.name}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          <p className="text-blue-100 mt-2">{generateSummary()}</p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8 space-y-12">
        {/* Executive Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              <span>Executive Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg leading-relaxed">
              {healthTags.length > 0 
                ? `Based on your quiz responses, we identified ${healthTags.length} key health area${healthTags.length > 1 ? 's' : ''} that could benefit from targeted nutritional support. This report provides personalized supplement and food recommendations to help you optimize your health and wellbeing.`
                : 'Based on your responses, you have a solid health foundation. This report provides general wellness recommendations to help you maintain and optimize your health.'
              }
            </p>
          </CardContent>
        </Card>

        {/* Health Areas */}
        {healthTags.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Health Areas to Focus On</CardTitle>
              <CardDescription>
                Key areas identified from your quiz responses
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {healthTags.map((tag, index) => (
                <div key={index} className="border-l-4 border-primary pl-4 py-2">
                  <h3 className="font-semibold text-lg text-primary">{index + 1}. {tag.name}</h3>
                  <p className="text-muted-foreground mt-1">{tag.description}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Quick Stats */}
        <div className="grid md:grid-cols-3 gap-6">
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-3xl font-bold text-primary">{supplements.length}</div>
              <p className="text-sm text-muted-foreground">Supplement Recommendations</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-3xl font-bold text-green-600">{foods.length}</div>
              <p className="text-sm text-muted-foreground">Food Recommendations</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-3xl font-bold text-orange-600">{healthTags.length}</div>
              <p className="text-sm text-muted-foreground">Health Areas</p>
            </CardContent>
          </Card>
        </div>

        {/* Start Smart Section */}
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-orange-800">
              <AlertTriangle className="h-5 w-5" />
              <span>Start Smart: One Supplement at a Time</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Begin with ONE supplement only. Wait 5-7 days before adding another. This helps you identify what works and avoid adverse reactions.
              </AlertDescription>
            </Alert>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-orange-800 mb-2">Week 1: Foundation</h4>
                <p className="text-sm text-muted-foreground">Start with your highest priority supplement. Monitor how you feel daily.</p>
              </div>
              <div>
                <h4 className="font-semibold text-orange-800 mb-2">Week 2: Assessment</h4>
                <p className="text-sm text-muted-foreground">If well-tolerated, prepare to add your second supplement.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Supplements Section */}
        {supplements.length > 0 && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold flex items-center space-x-2">
              <Pill className="h-6 w-6 text-primary" />
              <span>Your Supplement Recommendations</span>
            </h2>
            
            {supplements.map((supplement, index) => (
              <Card key={index} className="overflow-hidden">
                <CardHeader className={`${
                  supplement.score && supplement.score > 7 ? 'bg-red-50 border-b border-red-200' :
                  supplement.score && supplement.score > 4 ? 'bg-yellow-50 border-b border-yellow-200' :
                  'bg-green-50 border-b border-green-200'
                }`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-xl text-primary">
                        {index + 1}. {supplement.name}
                      </CardTitle>
                      {/* Health Tags Badges */}
                      {supplement.all_conditions && supplement.all_conditions.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {supplement.all_conditions.map((condition, i) => (
                            <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {condition}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      {supplement.dosage_info?.form && (
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">{supplement.dosage_info.form}</span>
                      )}
                      <PriorityBadge score={supplement.score} />
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-6 space-y-4">
                  <SafetyIndicator supplement={supplement} />
                  
                  <p className="text-muted-foreground">{supplement.description}</p>
                  
                  {/* Rationale */}
                  {supplement.rationale && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-semibold text-blue-800 mb-2 flex items-center">
                        <Target className="h-4 w-4 mr-2" />
                        Why This Was Recommended
                      </h4>
                      <p className="text-sm">{supplement.rationale}</p>
                    </div>
                  )}
                  
                  {/* Dosage Information */}
                  {supplement.dosage_info && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <h4 className="font-semibold text-green-800 flex items-center mb-2">
                        <Pill className="h-4 w-4 mr-2" />
                        Dosage Information
                      </h4>
                      <div className="space-y-2 text-sm">
                        {supplement.dosage_info.min_dose && (
                          <p><span className="font-medium">Starting dose:</span> {supplement.dosage_info.min_dose}</p>
                        )}
                        {supplement.dosage_info.max_dose && (
                          <p><span className="font-medium">Maximum safe dose:</span> {supplement.dosage_info.max_dose}</p>
                        )}
                        {supplement.dosage_info.timing && (
                          <p><span className="font-medium">Best time:</span> {supplement.dosage_info.timing}</p>
                        )}
                        {supplement.dosage_info.with_food !== undefined && (
                          <p><span className="font-medium">Food:</span> {supplement.dosage_info.with_food ? 'Take with food' : 'Take on empty stomach'}</p>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Benefits */}
                  <div>
                    <h4 className="font-semibold text-purple-800 mb-2">✨ Key Benefits</h4>
                    <ul className="grid md:grid-cols-2 gap-1 text-sm">
                      {supplement.benefits.map((benefit, i) => (
                        <li key={i} className="flex items-start">
                          <span className="text-purple-600 mr-2">•</span>
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  {/* Side Effects */}
                  {supplement.side_effects && supplement.side_effects.length > 0 && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h4 className="font-semibold text-yellow-800 mb-2">⚠️ Possible Side Effects</h4>
                      <ul className="text-sm space-y-1">
                        {supplement.side_effects.map((effect, i) => (
                          <li key={i} className="flex items-start">
                            <span className="text-yellow-600 mr-2">•</span>
                            {effect}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {/* Contraindications */}
                  {supplement.contraindications && supplement.contraindications.length > 0 && (
                    <Alert variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <span className="font-semibold">DO NOT TAKE IF:</span>
                        <ul className="list-disc list-inside mt-2 space-y-1">
                          {supplement.contraindications.map((item, i) => (
                            <li key={i}>{item}</li>
                          ))}
                        </ul>
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  {/* Interactions */}
                  {supplement.interactions && supplement.interactions.length > 0 && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <h4 className="font-semibold text-red-800 mb-2">🚨 Drug Interactions</h4>
                      <ul className="text-sm space-y-1">
                        {supplement.interactions.map((interaction, i) => (
                          <li key={i} className="flex items-start">
                            <span className="text-red-600 mr-2">•</span>
                            {interaction}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Foods Section */}
        {foods.length > 0 && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold flex items-center space-x-2">
              <Utensils className="h-6 w-6 text-green-600" />
              <span>Nourishing Food Recommendations</span>
            </h2>
            
            {foods.map((food, index) => (
              <Card key={index}>
                <CardHeader className="bg-green-50 border-b border-green-200">
                  <div>
                    <CardTitle className="text-xl text-green-700">
                      {index + 1}. {food.name}
                    </CardTitle>
                    {/* Health Tags Badges */}
                    {food.all_conditions && food.all_conditions.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {food.all_conditions.map((condition, i) => (
                          <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {condition}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </CardHeader>
                
                <CardContent className="pt-6 space-y-4">
                  <p className="text-muted-foreground">{food.description}</p>
                  
                  {/* Benefits */}
                  <div>
                    <h4 className="font-semibold text-green-800 mb-2">🌱 Health Benefits</h4>
                    <ul className="grid md:grid-cols-2 gap-1 text-sm">
                      {food.benefits.map((benefit, i) => (
                        <li key={i} className="flex items-start">
                          <span className="text-green-600 mr-2">•</span>
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  {/* Serving Suggestions */}
                  {food.serving_suggestions && food.serving_suggestions.length > 0 && (
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                      <h4 className="font-semibold text-orange-800 mb-2">👨‍🍳 How to Enjoy</h4>
                      <ul className="text-sm space-y-1">
                        {food.serving_suggestions.map((suggestion, i) => (
                          <li key={i} className="flex items-start">
                            <span className="text-orange-600 mr-2">•</span>
                            {suggestion}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Shopping Checklist */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <ShoppingCart className="h-5 w-5" />
              <span>Your Shopping Checklist</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {supplements.length > 0 && (
              <div>
                <h3 className="font-semibold text-primary mb-3">Supplements to Purchase:</h3>
                <div className="space-y-2">
                  {supplements.map((supplement, index) => (
                    <div key={index} className="flex items-center space-x-3 p-2 rounded hover:bg-gray-50">
                      <input type="checkbox" className="rounded" />
                      <span className={`text-lg ${
                        supplement.score && supplement.score > 7 ? '🔴' :
                        supplement.score && supplement.score > 4 ? '🟡' : '🟢'
                      }`}>
                        {supplement.score && supplement.score > 7 ? '🔴' :
                         supplement.score && supplement.score > 4 ? '🟡' : '🟢'}
                      </span>
                      <span className="flex-1">
                        {supplement.name}
                        {supplement.dosage_info?.form && ` (${supplement.dosage_info.form})`}
                        {supplement.dosage_info?.min_dose && ` - ${supplement.dosage_info.min_dose}`}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {foods.length > 0 && (
              <div>
                <h3 className="font-semibold text-green-700 mb-3">Foods to Add to Your Grocery List:</h3>
                <div className="space-y-2">
                  {foods.map((food, index) => (
                    <div key={index} className="flex items-center space-x-3 p-2 rounded hover:bg-gray-50">
                      <input type="checkbox" className="rounded" />
                      <span className="flex-1">{food.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Safety Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Safety First: Important Guidelines</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-base font-medium">
                ALWAYS consult your healthcare provider before starting any supplement regimen, especially if you take medications or have health conditions.
              </AlertDescription>
            </Alert>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-red-700 mb-2">When to Stop and Consult a Doctor</h4>
                <ul className="text-sm space-y-1">
                  {[
                    'Persistent nausea, headaches, or digestive issues',
                    'Unusual fatigue or energy changes',
                    'Skin rashes or allergic reactions',
                    'Changes in heart rate or blood pressure'
                  ].map((item, i) => (
                    <li key={i} className="flex items-start">
                      <span className="text-red-600 mr-2">•</span>
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold text-blue-700 mb-2">Quality Standards to Look For</h4>
                <ul className="text-sm space-y-1">
                  {[
                    'Third-party testing (USP, NSF, ConsumerLab)',
                    'GMP (Good Manufacturing Practice) certification',
                    'Clear ingredient lists (avoid proprietary blends)',
                    'Proper expiration dates and storage requirements'
                  ].map((item, i) => (
                    <li key={i} className="flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Reference */}
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader className="text-center">
            <CardTitle className="text-blue-800">📋 Quick Reference Guide</CardTitle>
            <CardDescription>Keep this summary handy for daily reference</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {supplements.length > 0 && (
              <div>
                <h3 className="font-semibold text-lg mb-3">Your Top Supplements</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  {supplements.slice(0, 4).map((supplement, index) => (
                    <div key={index} className="bg-white p-3 rounded border">
                      <h4 className="font-semibold text-blue-700">{index + 1}. {supplement.name}</h4>
                      {supplement.dosage_info?.min_dose && <p className="text-sm">Dosage: {supplement.dosage_info.min_dose}</p>}
                      {supplement.dosage_info?.timing && <p className="text-sm">Timing: {supplement.dosage_info.timing}</p>}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            <div>
              <h3 className="font-semibold text-lg mb-3">Daily Routine Checklist</h3>
              <div className="grid md:grid-cols-2 gap-2 text-sm">
                {[
                  'Take morning supplements with breakfast',
                  'Rate energy/mood (1-5) in journal',
                  'Eat 1-2 recommended foods',
                  'Take evening supplements (if any)',
                  'Note any changes or effects'
                ].map((item, i) => (
                  <div key={i} className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded" />
                    <span>{item}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <span className="font-semibold">⚠️ When to Contact Your Doctor:</span> Severe nausea, unusual heart palpitations, persistent headaches, or skin rashes
              </AlertDescription>
            </Alert>
            
            <p className="text-center text-xs text-gray-500 italic">
              Remember: Start with ONE supplement, monitor effects, consult healthcare providers
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}