# QuizHub - Personalized Supplement Quiz

A modern web application that provides personalized supplement recommendations based on user health assessments through an interactive quiz.

## Features

- **Interactive Health Quiz**: 10 comprehensive questions covering energy levels, sleep, joint health, stress, diet, and more
- **Personalized Analysis**: Smart logic that analyzes responses to identify key health areas
- **Detailed Recommendations**: Supplement and food recommendations with specific dosages and timing
- **Professional PDF Reports**: Comprehensive PDF reports with implementation schedules and disclaimers
- **Modern UI**: Built with React, TypeScript, and Shadcn/UI for a professional look and feel

## Tech Stack

- **Frontend**: Vite + React + TypeScript
- **Backend**: Deno + Oak (TypeScript)
- **UI Components**: Shadcn/UI with Tailwind CSS
- **PDF Generation**: jsPDF for client-side PDF creation
- **Payments**: Stripe integration
- **Styling**: Tailwind CSS with custom design system
- **Build Tool**: Vite for fast development and optimized builds

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Deno (install from https://deno.land/install.sh)

### Installation

1. Clone the repository:
   ```bash
   git clone <your-repo-url>
   cd quizhub
   ```

2. Install frontend dependencies:
   ```bash
   npm install
   ```

3. Set up backend environment (optional for basic functionality):
   ```bash
   cd server
   cp .env.example .env
   # Edit .env with your Stripe keys if needed
   ```

4. Start both frontend and backend:
   ```bash
   npm run dev:full
   ```
   
   Or start them separately:
   ```bash
   # Terminal 1 - Backend
   npm run dev:server
   
   # Terminal 2 - Frontend  
   npm run dev
   ```

5. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` folder, ready for deployment.

## User Journey

1. **Landing Page**: Welcome screen with benefits and call-to-action
2. **Health Quiz**: 10 interactive questions with progress tracking
3. **Results Preview**: Personalized health analysis and recommendations
4. **Payment Simulation**: Mock payment flow ($2 pricing)
5. **PDF Generation**: Automatic download of comprehensive health report

## Architecture

### Frontend (Vite + React)
- Client-side UI and PDF generation
- Calls backend API for quiz processing
- Handles Stripe payments (client-side integration)

### Backend (Deno + Oak)
- Stateless quiz processing (no database required for basic functionality)
- Secure payment handling via Stripe webhooks
- API endpoints for quiz analysis and recommendations
- Easy to deploy as Deno Deploy edge functions

**Why Deno?** 
- TypeScript-first runtime (matches frontend)
- Excellent for serverless/edge deployment
- Simple dependency management
- Perfect for Supabase Edge Functions if needed later

## Quiz Logic

The backend analyzes quiz responses to identify health areas:

- **Low Energy**: Triggered by "Very low" or "Low" energy responses
- **Sleep Issues**: Triggered by "Yes" to sleep problems
- **Joint Support**: Triggered by "Yes" to joint pain/stiffness
- **Stress Management**: Triggered by "Yes" to regular stress
- **Digestive Support**: Triggered by "Poor" or "Fair" digestive health
- **Physical Activity**: Triggered by low exercise frequency

Each health area generates specific supplement and food recommendations with detailed dosages, timing, and benefits.

## PDF Report Contents

- Executive summary based on quiz responses
- Health areas to focus on
- Detailed supplement recommendations with dosages and timing
- Food recommendations with nutritional benefits
- 30-day implementation schedule
- Important notes and medical disclaimers

## Customization

The quiz questions, health logic, and recommendations can be easily modified in:

- **Questions**: `src/pages/Quiz.tsx` - questions array
- **Processing Logic**: `src/pages/Results.tsx` - processAnswers function
- **PDF Template**: `src/utils/pdfGenerator.ts` - generateHealthReportPDF function

## Deployment

This is a static site that can be deployed to:

- **Cloudflare Pages** (recommended for performance)
- **Vercel**
- **Netlify** 
- **GitHub Pages**
- Any static hosting service

## Future Enhancements

- Integration with Supabase for dynamic questions and user accounts
- Stripe integration for real payments
- Email sequences for user retention
- Admin dashboard for content management
- Mobile app version
- Multi-language support

## License

This project is licensed under the MIT License.

## Disclaimer

This application is for informational purposes only and is not intended as medical advice. Users should always consult with healthcare professionals before starting any supplement regimen.