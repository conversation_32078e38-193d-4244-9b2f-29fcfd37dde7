import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useQuestionsStatic } from '@/hooks/useQuestionsStatic'
import { Loader2 } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface QuizProps {
  onComplete: (answers: Record<string, any>) => void
  isDirectPayment?: boolean
}


export function Quiz({ onComplete, isDirectPayment = false }: QuizProps) {
  const { questions, loading, error } = useQuestionsStatic()
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null)
  const [card<PERSON><PERSON>, set<PERSON>ard<PERSON>ey] = useState(0)
  const [transitionDirection, setTransitionDirection] = useState<'out' | 'in' | 'entering'>('in')

  useEffect(() => {
    if (isDirectPayment && questions.length > 0) {
      const autoAnswers: Record<string, string> = {}
      questions.forEach(question => {
        autoAnswers[question.id] = question.options[0]
      })
      
      setTimeout(() => {
        onComplete(autoAnswers)
      }, 1000)
    }
  }, [isDirectPayment, questions, onComplete])

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="flex items-center justify-center p-8">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>{isDirectPayment ? 'Auto-completing quiz...' : 'Loading quiz questions...'}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                Failed to load quiz questions: {error}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    )
  }

  // No questions available
  if (!questions.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Alert>
              <AlertDescription>
                No quiz questions are currently available.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    )
  }

  const currentQuestion = questions[currentQuestionIndex]
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100

  const handleAnswer = (answer: string) => {
    if (isTransitioning || selectedAnswer) return // Prevent multiple clicks

    // Step 1: Show selected answer immediately
    setSelectedAnswer(answer)

    // Step 2: Save the answer
    const newAnswers = { ...answers, [currentQuestion.id]: answer }
    setAnswers(newAnswers)

    // Step 3: Wait 750ms, then start horizontal transition
    setTimeout(() => {
      if (currentQuestionIndex < questions.length - 1) {
        // Start transition - current card slides out to right
        setTransitionDirection('out')
        setIsTransitioning(true)

        // After slide out animation, change question and prepare new card from right
        setTimeout(() => {
          setCurrentQuestionIndex(currentQuestionIndex + 1)
          setCardKey(prev => prev + 1)
          setSelectedAnswer(null) // Reset selection for next question
          setTransitionDirection('entering')
          
          // Start new card from right edge, then animate smoothly to center
          setTimeout(() => {
            setTransitionDirection('in')
            setIsTransitioning(false)
          }, 50)
        }, 200)
      } else {
        // Complete quiz
        setTimeout(() => {
          onComplete(newAnswers)
        }, 300)
      }
    }, 750)
  }

  return (
    <>
      {/* Fixed progress bar under sign in area */}
      <div className="fixed top-16 left-0 right-0 z-50">
        <div className="h-2 bg-emerald-100/40">
          <div
            className="h-2 bg-gradient-to-r from-emerald-500 via-teal-500 to-green-600 transition-all duration-700 ease-out rounded-r-full shadow-sm"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50 flex items-center justify-center p-4 pt-20">
        <div className="w-full max-w-2xl overflow-hidden">
          <Card
            key={cardKey}
            className={`w-full transition-transform duration-200 ease-out shadow-xl hover:shadow-2xl bg-white/95 backdrop-blur-sm border border-emerald-200/50`}
            style={{
              transform: isTransitioning && transitionDirection === 'out'
                ? 'translateX(-100%)'
                : transitionDirection === 'entering'
                ? 'translateX(100%)'
                : 'translateX(0)'
            }}
          >
            <CardHeader>
              <CardTitle className="text-2xl font-semibold text-slate-800">
                {currentQuestion.text}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {currentQuestion.options.map((option, index) => {
                const isSelected = selectedAnswer === option
                const isDisabled = selectedAnswer !== null || isTransitioning

                return (
                  <Button
                    key={index}
                    variant={isSelected ? "default" : "outline"}
                    disabled={isDisabled}
                    className={`w-full justify-start text-left p-4 h-auto transition-all duration-300 ease-in-out
                      border-2 transform-gpu ${
                        isSelected
                          ? '!bg-emerald-600 !text-white !border-emerald-600 scale-[1.02] shadow-lg shadow-emerald-200/50'
                          : isDisabled
                          ? 'cursor-not-allowed bg-gray-50 border-gray-200 text-gray-400 opacity-50'
                          : 'hover:bg-emerald-50 hover:border-emerald-300 hover:scale-[1.01] hover:shadow-md bg-white border-emerald-200/50 text-slate-700 hover:text-emerald-800'
                      }`}
                    onClick={() => handleAnswer(option)}
                  >
                    <span className="text-base leading-relaxed">{option}</span>
                  </Button>
                )
              })}
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  )
}