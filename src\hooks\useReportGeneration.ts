import { useState } from 'react'
import { useQuizResultsAPI } from './useQuizResultsAPI'
import { generateHealthReportPDF } from '@/utils/pdfGenerator'

export function useReportGeneration() {
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { processResults } = useQuizResultsAPI()

  const generateReport = async (answers: Record<string, string>) => {
    try {
      setGenerating(true)
      setError(null)

      // Process quiz results to get recommendations
      const results = await processResults(answers)

      // Convert results to the format expected by PDF generator
      const healthTagsMap = new Map<string, { name: string; description: string }>()
      const recommendations: Array<{
        type: 'supplement' | 'food'
        name: string
        description: string
        benefits: string[]
        dosage?: string
        timing?: string
        side_effects?: string[]
        contraindications?: string[]
        max_dose?: string
        form?: string
        with_food?: boolean
        interactions?: string[]
        pregnancy_safe?: boolean
        breastfeeding_safe?: boolean
        rationale?: string
        priority_score?: number
        all_conditions?: string[]
        nutritional_info?: any
        serving_suggestions?: string[]
      }> = []

      results.forEach(result => {
        // Collect unique health tags
        if (!healthTagsMap.has(result.health_tag_name)) {
          healthTagsMap.set(result.health_tag_name, {
            name: result.health_tag_name,
            description: result.health_tag_description
          })
        }

        // Add recommendation with all available data
        recommendations.push({
          type: result.recommendation_type,
          name: result.recommendation_name,
          description: result.recommendation_details.description,
          benefits: result.recommendation_details.benefits,
          dosage: result.recommendation_details.dosage_info?.min_dose,
          timing: result.recommendation_details.dosage_info?.timing,
          // Enhanced supplement data
          side_effects: result.recommendation_details.side_effects,
          contraindications: result.recommendation_details.contraindications,
          max_dose: result.recommendation_details.dosage_info?.max_dose,
          form: result.recommendation_details.dosage_info?.form,
          with_food: result.recommendation_details.dosage_info?.with_food,
          interactions: result.recommendation_details.interactions,
          pregnancy_safe: result.recommendation_details.pregnancy_safe,
          breastfeeding_safe: result.recommendation_details.breastfeeding_safe,
          rationale: result.recommendation_details.rationale,
          priority_score: result.priority_score,
          all_conditions: result.recommendation_details.all_conditions,
          // Enhanced food data
          nutritional_info: result.recommendation_details.nutritional_info,
          serving_suggestions: result.recommendation_details.serving_suggestions
        })
      })

      // Generate PDF
      await generateHealthReportPDF({
        healthTags: Array.from(healthTagsMap.values()),
        recommendations,
        answers
      })

      return true
    } catch (err) {
      console.error('Error generating report:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate report'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setGenerating(false)
    }
  }

  return { generateReport, generating, error }
}