import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface PreTestProps {
  onStartQuiz: () => void
}

type CardStep = 'hi' | 'before-start' | 'lets-start'

export function PreTest({ onStartQuiz }: PreTestProps) {
  const [currentStep, setCurrentStep] = useState<CardStep>('hi')
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [transitionDirection, setTransitionDirection] = useState<'out' | 'in' | 'entering'>('in')
  const [cardKey, setCardKey] = useState(0)

  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentStep === 'hi') {
        // Transition to "before-start" card
        setIsTransitioning(true)
        setTransitionDirection('out')

        setTimeout(() => {
          setCurrentStep('before-start')
          setCardKey(prev => prev + 1)
          setTransitionDirection('entering')

          setTimeout(() => {
            setTransitionDirection('in')
            setIsTransitioning(false)
          }, 50)
        }, 300)
      } else if (currentStep === 'before-start') {
        // Transition to "lets-start" card
        setIsTransitioning(true)
        setTransitionDirection('out')

        setTimeout(() => {
          setCurrentStep('lets-start')
          setCardKey(prev => prev + 1)
          setTransitionDirection('entering')

          setTimeout(() => {
            setTransitionDirection('in')
            setIsTransitioning(false)
          }, 50)
        }, 300)
      } else if (currentStep === 'lets-start') {
        // Start the quiz
        onStartQuiz()
      }
    }, 2000) // 2 seconds for each card

    return () => clearTimeout(timer)
  }, [currentStep, onStartQuiz])

  const renderHiCard = () => (
    <div className="text-center">
      <div className="text-4xl mb-4 animate-bounce">👋</div>
      <h1 className="text-4xl font-bold text-gray-900">Hi!</h1>
    </div>
  )

  const renderBeforeStartCard = () => (
    <div className="text-center space-y-8">
      {/* Simplified icon */}
      <div className="flex justify-center mb-6">
        <div className="w-20 h-20 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-full flex items-center justify-center">
          <div className="text-2xl">📋</div>
        </div>
      </div>

      <h1 className="text-3xl font-bold text-gray-900 mb-6">
        Before you start...
      </h1>

      {/* Privacy statements */}
      <div className="space-y-4 text-left max-w-md mx-auto">
        <div className="flex items-start gap-3">
          <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <p className="text-lg text-gray-900 leading-relaxed">
            Your answers are anonymized, and we do not sell your data.
          </p>
        </div>

        <div className="flex items-start gap-3">
          <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <p className="text-lg text-gray-900 leading-relaxed">
            Answer each question honestly for most accurate results.
          </p>
        </div>
      </div>
    </div>
  )

  const renderLetsStartCard = () => (
    <div className="text-center">
      <div className="text-4xl mb-4 animate-pulse">🚀</div>
      <h1 className="text-4xl font-bold text-emerald-600">Let's start!</h1>
    </div>
  )

  const getCurrentCard = () => {
    switch (currentStep) {
      case 'hi':
        return renderHiCard()
      case 'before-start':
        return renderBeforeStartCard()
      case 'lets-start':
        return renderLetsStartCard()
      default:
        return renderHiCard()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-green-50 flex flex-col items-center justify-center p-4">
      {/* Logo */}
      <div className="mb-16">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
            <div className="w-4 h-4 bg-white rounded-sm"></div>
          </div>
          <span className="text-2xl font-medium text-emerald-600">LifeSupplier</span>
        </div>
      </div>

      <div className="w-full max-w-lg overflow-hidden">
        {currentStep === 'before-start' ? (
          <Card
            key={cardKey}
            className={`w-full transition-transform duration-300 ease-out shadow-xl bg-white/95 backdrop-blur-sm border border-emerald-200/50 p-8`}
            style={{
              transform: isTransitioning && transitionDirection === 'out'
                ? 'translateX(-100%)'
                : transitionDirection === 'entering'
                ? 'translateX(100%)'
                : 'translateX(0)'
            }}
          >
            <div className="flex items-center justify-center min-h-[300px]">
              {getCurrentCard()}
            </div>
          </Card>
        ) : (
          <div
            key={cardKey}
            className={`w-full transition-transform duration-300 ease-out`}
            style={{
              transform: isTransitioning && transitionDirection === 'out'
                ? 'translateX(-100%)'
                : transitionDirection === 'entering'
                ? 'translateX(100%)'
                : 'translateX(0)'
            }}
          >
            <div className="flex items-center justify-center min-h-[200px]">
              {getCurrentCard()}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}