import { Button } from '@/components/ui/button'

interface LandingProps {
  onStartQuiz: () => void
}

export function Landing({ onStartQuiz }: LandingProps) {
  return (
    <div className="min-h-screen bg-white">
      {/* Header with Logo */}
      <header className="py-6 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm"></div>
            </div>
            <span className="text-2xl font-medium text-emerald-600">LifeSupplier</span>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-white to-green-50 py-20 px-4">
        <div className="max-w-6xl mx-auto">
          {/* First Row - Title and Image */}
          <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
            {/* Left Column - Title and Trust Badge */}
            <div className="text-center lg:text-left">
              {/* Trust Badge */}
              <div className="inline-flex items-center bg-green-100 text-green-800 px-6 py-3 rounded-full text-sm font-semibold mb-6">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Trusted by 25,000+ Health-Conscious Individuals
              </div>
              
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight">
                Discover Your Personalized Supplement Plan
              </h1>
            </div>
            
            {/* Right Column - Image */}
            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                <img 
                  src="/src/assets/woman_healthy_fruits.jpg" 
                  alt="Happy woman enjoying healthy fruits and supplements" 
                  className="w-full h-auto object-cover"
                />
                {/* Interactive overlay with CTA */}
                <div className="absolute bottom-6 left-6 right-6">
                  <div className="bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm font-semibold text-gray-900">Join Sarah & 25,000+ others</div>
                        <div className="text-xs text-gray-600">Who found their perfect supplement plan</div>
                      </div>
                      <Button
                        onClick={onStartQuiz}
                        size="sm"
                        className="bg-orange-500 hover:bg-orange-600 text-white text-xs px-4 py-2 rounded-full font-semibold"
                      >
                        Start Now
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Second Row - Description and Main CTA */}
          <div className="text-center max-w-4xl mx-auto">
            <p className="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed">
              Take our 5-minute science-based health assessment and receive a custom nutrient blend designed specifically for your unique health profile and goals.
            </p>
            
            <div className="space-y-4">
              <Button
                onClick={onStartQuiz}
                size="lg"
                className="bg-orange-500 hover:bg-orange-600 text-white text-xl sm:text-2xl px-6 sm:px-8 py-5 sm:py-6 rounded-full font-bold shadow-lg transform transition-all duration-200 hover:scale-105 w-full max-w-sm sm:max-w-md mx-auto flex items-center justify-center text-center min-h-[60px] sm:min-h-[70px]"
              >
                <span className="text-center leading-tight tracking-wide">
                  <span className="hidden sm:inline">Take Your Health Quiz</span>
                  <span className="sm:hidden">Start Health Quiz</span>
                </span>
              </Button>

              <p className="text-base sm:text-sm text-gray-500 px-4">
                ⏱️ 5 minutes • 🆓 personalized report • 🔒 100% private & secure
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">How It Works</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Get your personalized supplement recommendations in two simple steps
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Image */}
            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                <img 
                  src="/src/assets/woman_running_golden_sun.jpg" 
                  alt="Woman running in golden sunlight, representing health and vitality" 
                  className="w-full h-auto object-cover"
                />
                {/* Overlay with motivational text */}
                <div className="absolute bottom-6 left-6 right-6">
                  <div className="bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg">
                    <div className="text-center">
                      <div className="text-sm font-semibold text-gray-900">Start Your Health Journey</div>
                      <div className="text-xs text-gray-600">Discover what your body truly needs</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Right Column - How It Works Steps */}
            <div className="space-y-8">
              <div className="flex items-start">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-6 flex-shrink-0">
                  <span className="text-2xl font-bold text-blue-600">01</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">Answer Health Questions</h3>
                  <p className="text-gray-600 leading-relaxed">
                    Complete our comprehensive 5-minute assessment covering your health goals, dietary habits, lifestyle, and current symptoms.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mr-6 flex-shrink-0">
                  <span className="text-2xl font-bold text-green-600">02</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">Get Personalized Report</h3>
                  <p className="text-gray-600 leading-relaxed">
                    Receive a detailed analysis created by registered nutritionists with specific supplement recommendations tailored to your needs.
                  </p>
                </div>
              </div>
              
              <div className="pt-4">
                <Button
                  onClick={onStartQuiz}
                  size="lg"
                  className="bg-orange-500 hover:bg-orange-600 text-white text-lg sm:text-xl px-6 sm:px-8 py-4 sm:py-5 rounded-full font-bold w-full max-w-[300px] sm:max-w-sm mx-auto flex items-center justify-center text-center min-h-[56px] sm:min-h-[64px]"
                >
                  <span className="text-center leading-tight tracking-wide">Start Assessment</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Why Personalized Nutrition Matters</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Generic multivitamins can't address your unique nutritional needs. Our personalized approach ensures you get exactly what your body requires.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Targeted Results</h3>
              <p className="text-gray-600 text-sm">
                Address your specific health concerns with precision-formulated recommendations
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🧬</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Science-Based</h3>
              <p className="text-gray-600 text-sm">
                Backed by peer-reviewed research and nutritional science expertise
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💊</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Premium Quality</h3>
              <p className="text-gray-600 text-sm">
                Third-party tested, pharmaceutical-grade ingredients from trusted sources
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📈</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Better Absorption</h3>
              <p className="text-gray-600 text-sm">
                Optimized nutrient combinations and timing for maximum bioavailability
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Scientific Rationale Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">The Science Behind Personalization</h2>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                Your age, gender, lifestyle, stress levels, diet, and health goals all impact your nutritional requirements. Our assessment analyzes these factors to create a custom formula that works specifically for you.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Nutrient Interaction Analysis</h4>
                    <p className="text-gray-600 text-sm">We consider how different vitamins and minerals work together or compete for absorption.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Bioavailability Optimization</h4>
                    <p className="text-gray-600 text-sm">Timing and form recommendations to maximize nutrient uptake and utilization.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Individual Health Profiling</h4>
                    <p className="text-gray-600 text-sm">Your unique health markers guide precise dosage and ingredient selection.</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-2xl shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Sample Supplement Facts</h3>
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <img 
                  src="/src/assets/supplement_example.png" 
                  alt="Example supplement recommendation from personalized report" 
                  className="w-full h-auto rounded-lg shadow-sm"
                />
              </div>
              <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-sm text-blue-800 font-medium mb-1">
                  📋 Example from Actual Report
                </p>
                <p className="text-xs text-blue-600">
                  This shows how supplement recommendations appear in your personalized report. Each recommendation includes detailed information that can be expanded for more details.
                </p>
              </div>
              <p className="text-xs text-gray-500 mt-4">
                *Actual recommendations vary based on your individual assessment results
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Research-Based Section */}
      <section className="py-20 px-4 bg-blue-50">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">Developed Through Research</h2>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                Our assessment methodology is built upon decades of peer-reviewed nutritional research from leading medical institutions and health organizations worldwide. Every recommendation is backed by scientific evidence and established clinical guidelines.
              </p>
              
              <div className="space-y-4 mb-8">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Evidence-Based Algorithms</h4>
                    <p className="text-gray-600 text-sm">Built on research from top medical institutions and health organizations</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Clinical Guidelines</h4>
                    <p className="text-gray-600 text-sm">Follows established nutritional standards and safety protocols</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Peer-Reviewed Sources</h4>
                    <p className="text-gray-600 text-sm">All recommendations backed by published scientific studies</p>
                  </div>
                </div>
              </div>
              
              <div className="text-center">
                <Button
                  onClick={onStartQuiz}
                  size="lg"
                  className="bg-orange-500 hover:bg-orange-600 text-white text-base sm:text-lg px-4 sm:px-8 py-3 sm:py-4 rounded-full font-semibold w-full max-w-[280px] sm:max-w-xs mx-auto flex items-center justify-center text-center"
                >
                  <span className="text-center leading-tight">Start Assessment</span>
                </Button>
              </div>
            </div>
            
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <h3 className="font-bold text-gray-900 mb-6 text-center">Research Sources & Partners</h3>
                <div className="grid grid-cols-3 gap-4 items-center justify-items-center">
                  <img 
                    src="/src/assets/institutes_logos/World_Health_Organization_Logo.svg.png" 
                    alt="World Health Organization" 
                    className="h-12 w-auto object-contain"
                  />
                  <img 
                    src="/src/assets/institutes_logos/charite-logo.svg" 
                    alt="Charité Berlin" 
                    className="h-10 w-auto object-contain"
                  />
                  <img 
                    src="/src/assets/institutes_logos/BIH_Logo_at-Charite_kurz_hoch_rgb.png" 
                    alt="Berlin Institute of Health" 
                    className="h-16 w-auto object-contain"
                  />
                  <img 
                    src="/src/assets/institutes_logos/MedlinePlus-logo-300x175.png" 
                    alt="MedlinePlus Health" 
                    className="h-18 w-auto object-contain"
                  />
                  <img 
                    src="/src/assets/institutes_logos/Psychology-today-logo.png" 
                    alt="Psychology Today" 
                    className="h-16 w-auto object-contain"
                  />
                  <img 
                    src="/src/assets/institutes_logos/Womens-Health-logo.png" 
                    alt="Women's Health" 
                    className="h-16 w-auto object-contain"
                  />
                  <img 
                    src="/src/assets/institutes_logos/Forbes_logo.svg.png" 
                    alt="Forbes" 
                    className="h-6 w-auto object-contain"
                  />
                  <img 
                    src="/src/assets/institutes_logos/NewYorkTimes.svg.png" 
                    alt="The New York Times" 
                    className="h-6 w-auto object-contain"
                  />
                  <img 
                    src="/src/assets/institutes_logos/Healthline_logo.svg.png" 
                    alt="Healthline" 
                    className="h-6 w-auto object-contain"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-4 text-center">
                  Our methodology references research from these and other leading health institutions
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Offer & Guarantee */}
      <section className="py-20 px-4 bg-gradient-to-r from-orange-500 to-red-500 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-4">Complete Your Assessment</h2>
          <p className="text-xl mb-8 opacity-90">
            Includes comprehensive personalized report
          </p>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8">
            <h3 className="text-2xl font-bold mb-4">What You'll Receive:</h3>
            <div className="grid md:grid-cols-2 gap-6 text-left">
              <div className="space-y-3">
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                  <span>Personalized supplement recommendations</span>
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                  <span>Precise dosages and timing instructions</span>
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                  <span>30-day implementation schedule</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                  <span>Food source alternatives</span>
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                  <span>Nutrient interaction warnings</span>
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                  <span>Progress tracking guidance</span>
                </div>
              </div>
            </div>
          </div>
          
          <Button
            onClick={onStartQuiz}
            size="lg"
            className="bg-white text-orange-600 hover:bg-gray-100 text-base sm:text-lg md:text-xl px-4 sm:px-8 md:px-12 py-4 sm:py-5 md:py-6 rounded-full font-bold shadow-lg flex items-center justify-center text-center min-h-[3.5rem] sm:min-h-[4rem] w-full max-w-[320px] sm:max-w-md mx-auto"
          >
            <span className="text-center leading-tight px-2">
              <span className="hidden md:inline">Claim Your Comprehensive Assessment Now</span>
              <span className="hidden sm:inline md:hidden">Get Your Assessment Now</span>
              <span className="sm:hidden">Start Assessment</span>
            </span>
          </Button>
          
          <div className="mt-6 flex justify-center items-center space-x-8 text-sm opacity-80">
            <div className="flex items-center">
              <span className="mr-2">⚡</span>
              <span>Instant Access</span>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-600">Get answers to common questions about our personalized nutrition assessment</p>
          </div>
          
          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 className="font-bold text-gray-900 mb-3">How accurate are the recommendations?</h3>
              <p className="text-gray-600 leading-relaxed">
                Our recommendations are based on peer-reviewed nutritional research and established RDA guidelines. The assessment considers your individual factors including age, gender, lifestyle, diet, and health goals to provide personalized suggestions that are significantly more accurate than generic multivitamins.
              </p>
            </div>
            
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 className="font-bold text-gray-900 mb-3">Is my personal information secure?</h3>
              <p className="text-gray-600 leading-relaxed">
                Absolutely. We use bank-level encryption to protect your data and never share your personal information with third parties. Your assessment responses are used solely to generate your personalized report and are stored securely according to HIPAA guidelines.
              </p>
            </div>
            
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 className="font-bold text-gray-900 mb-3">What if I'm not satisfied with my report?</h3>
              <p className="text-gray-600 leading-relaxed">
                We offer a 30-day money-back guarantee. If you're not completely satisfied with your personalized report, simply contact our support team for a full refund. We're confident you'll find valuable insights that help improve your health.
              </p>
            </div>
            
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 className="font-bold text-gray-900 mb-3">Do I need to consult my doctor?</h3>
              <p className="text-gray-600 leading-relaxed">
                While our recommendations are based on established nutritional science, we always recommend consulting with your healthcare provider before starting any new supplement regimen, especially if you have existing health conditions or take medications.
              </p>
            </div>
            
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 className="font-bold text-gray-900 mb-3">How detailed is the personalized report?</h3>
              <p className="text-gray-600 leading-relaxed">
                Your report includes specific supplement recommendations with exact dosages, optimal timing for each nutrient, food source alternatives, a 30-day implementation schedule, progress tracking guidelines, and important nutrient interaction information. Most reports are 12-15 pages of actionable insights.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 px-4 bg-gray-900 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Transform Your Health?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands who've discovered their perfect supplement plan through our science-based assessment.
          </p>
          
          <div className="text-center mb-6">
            <Button
              onClick={onStartQuiz}
              size="lg"
              className="bg-orange-500 hover:bg-orange-600 text-white text-base sm:text-lg px-4 sm:px-8 py-3 sm:py-4 rounded-full font-semibold w-full max-w-[280px] sm:max-w-xs mx-auto flex items-center justify-center text-center"
            >
              <span className="text-center leading-tight">Start Assessment</span>
            </Button>
          </div>
          
          <p className="text-sm opacity-75">
            ⏰ Instant digital delivery
          </p>
        </div>
      </section>

      {/* Footer with Trust Badges */}
      <footer className="py-12 px-4 bg-gray-50 border-t">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-green-600">🔒</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-1">Secure & Private</h4>
              <p className="text-sm text-gray-600">Bank-level encryption</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600">🧬</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-1">Science-Based</h4>
              <p className="text-sm text-gray-600">Peer-reviewed research</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-orange-600">⚡</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-1">Instant Results</h4>
              <p className="text-sm text-gray-600">Immediate digital delivery</p>
            </div>
          </div>
          
          <div className="text-center border-t pt-8">
            <p className="text-sm text-gray-500">
              © 2025 Personalized Nutrition Assessment. All rights reserved. | 
              <span className="mx-2">Privacy Policy</span> | 
              <span className="mx-2">Terms of Service</span> | 
              <span className="mx-2">Contact Support</span>
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}