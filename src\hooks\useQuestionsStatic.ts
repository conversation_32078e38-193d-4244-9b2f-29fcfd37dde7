import { useState, useEffect } from 'react'

interface Question {
  id: string
  text: string
  type: 'yes-no' | 'multiple-choice'
  options: string[]
  order: number
}

// Comprehensive quiz questions based on the CSV data
const QUIZ_QUESTIONS: Question[] = [
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    text: 'What is your gender?',
    type: 'multiple-choice',
    options: ['Male', 'Female', 'Other'],
    order: 1
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    text: 'What is your age range?',
    type: 'multiple-choice',
    options: ['18-25', '26-35', '36-45', '46-55', '56-65', '65+'],
    order: 2
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    text: 'How would you describe your energy levels?',
    type: 'multiple-choice',
    options: ['Very low', 'Low', 'Moderate', 'High', 'Very high'],
    order: 3
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440004',
    text: 'Do you have trouble sleeping?',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 4
  },
  {
    id: '598022d6-cece-4d65-a457-dcfe80a3a1fb',
    text: 'I wake up feeling rested and refreshed.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 5
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440005',
    text: 'Do you experience joint pain or stiffness?',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 6
  },
  {
    id: 'b941ea42-0943-49e1-95a3-462f3debcc03',
    text: 'My joints and hips feel flexible and comfortable.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 7
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440006',
    text: 'How often do you exercise?',
    type: 'multiple-choice',
    options: ['Never', '1-2 times per week', '3-4 times per week', '5+ times per week'],
    order: 8
  },
  {
    id: 'ce586653-1155-4563-839f-266623795bae',
    text: 'I feel physically strong and have good stamina.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 9
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440008',
    text: 'Do you experience stress regularly?',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 10
  },
  {
    id: '5b3879a5-e825-4fff-b786-b7bc1b4cc025',
    text: 'I maintain a healthy work-life balance.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 11
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440009',
    text: 'How would you rate your digestive health?',
    type: 'multiple-choice',
    options: ['Poor', 'Fair', 'Good', 'Excellent'],
    order: 12
  },
  {
    id: '33ddc48a-3741-428b-b877-173b0168ebf9',
    text: 'I can focus and concentrate well throughout the day.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 13
  },
  {
    id: '452ac791-288b-48aa-98ab-80d2173b2240',
    text: 'I crave sugar, or I suddenly feel weak and lost.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 14
  },
  {
    id: '2eb69b8e-4cbb-497d-bc90-cc2ceb10b3bc',
    text: 'I don\'t get easily sick or get the flu.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 15
  },
  {
    id: 'b2254912-7bb7-4428-a365-b7c0de7c8bf5',
    text: 'I eat fresh fruits and vegetables daily.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 16
  },
  {
    id: 'f232fa4c-4268-4d41-8e67-e0b71d67c4bd',
    text: 'I drink enough water to stay hydrated every day.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 17
  },
  {
    id: 'aeda2a7e-b897-4bc0-a67f-20f1306c83d0',
    text: 'My hair is healthy and not thinning.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 18
  },
  {
    id: 'e2715890-51c7-4582-a89e-5007c3efb634',
    text: 'I breathe comfortably and deeply without difficulty.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 19
  },
  {
    id: 'c19fca54-eef1-460c-9c2a-abb5753f39f6',
    text: 'I use caffeine regularly.',
    type: 'yes-no',
    options: ['Yes', 'No'],
    order: 20
  }
]

export function useQuestionsStatic() {
  const [questions, setQuestions] = useState<Question[]>([])
  const [loading, setLoading] = useState(true)
  const [error] = useState<string | null>(null)

  useEffect(() => {
    // Simulate loading for smooth UX
    const timer = setTimeout(() => {
      setQuestions(QUIZ_QUESTIONS)
      setLoading(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  return { questions, loading, error }
}