import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

export async function generatePDFFromHTML(elementId: string, filename: string): Promise<void> {
  try {
    const element = document.getElementById(elementId)
    if (!element) {
      throw new Error(`Element with id '${elementId}' not found`)
    }

    // Configure html2canvas for better quality
    const canvas = await html2canvas(element, {
      allowTaint: true,
      useCORS: true,
      scale: 2, // Higher scale for better quality
      scrollX: 0,
      scrollY: 0,
      backgroundColor: '#ffffff',
      logging: false,
      onclone: (clonedDoc) => {
        // Add print-specific styles to the cloned document
        const printStyles = `
          @media print {
            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .print\\:hidden {
              display: none !important;
            }
            .print\\:page-break-before {
              page-break-before: always !important;
            }
          }
        `
        const style = clonedDoc.createElement('style')
        style.innerHTML = printStyles
        clonedDoc.head.appendChild(style)
      }
    })

    const imgData = canvas.toDataURL('image/png')
    
    // Calculate PDF dimensions
    const imgWidth = canvas.width
    const imgHeight = canvas.height
    const ratio = Math.min(210 / imgWidth, 297 / imgHeight) // A4 size in mm
    const pdfWidth = imgWidth * ratio
    const pdfHeight = imgHeight * ratio

    // Create PDF
    const pdf = new jsPDF('p', 'mm', 'a4')
    
    // If content is taller than one page, create multiple pages
    const pageHeight = 297 // A4 height in mm
    let heightLeft = pdfHeight
    let position = 0

    // Add first page
    pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, pdfHeight)
    heightLeft -= pageHeight

    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - pdfHeight
      pdf.addPage()
      pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, pdfHeight)
      heightLeft -= pageHeight
    }

    // Save the PDF
    pdf.save(filename)
  } catch (error) {
    console.error('Error generating PDF:', error)
    throw error
  }
}

export async function generateComprehensivePDF(elementId: string): Promise<void> {
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).replace(/\s/g, '-')
  
  const filename = `QuizHub-Health-Report-${currentDate}.pdf`
  
  await generatePDFFromHTML(elementId, filename)
}